{"info": {"_postman_id": "12345678-1234-1234-1234-123456789012", "name": "Home Services Authentication API", "description": "Complete API collection for Home Services Authentication Service", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "Register Customer/Provider", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"mobile_number\": \"+919876543210\",\n    \"name\": \"<PERSON>\",\n    \"user_type\": \"CUSTOMER\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/register/mobile/", "host": ["{{base_url}}"], "path": ["api", "auth", "register", "mobile", ""]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set(\"user_id\", response.user_id);", "}"], "type": "text/javascript"}}]}, {"name": "Send OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"mobile_number\": \"+919876543210\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/otp/send/", "host": ["{{base_url}}"], "path": ["api", "auth", "otp", "send", ""]}}}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"mobile_number\": \"+919876543210\",\n    \"otp\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/otp/verify/", "host": ["{{base_url}}"], "path": ["api", "auth", "otp", "verify", ""]}}}, {"name": "Login with Mobile/OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"mobile_number\": \"+919876543210\",\n    \"otp\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login/mobile/", "host": ["{{base_url}}"], "path": ["api", "auth", "login", "mobile", ""]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set(\"access_token\", response.tokens.access);", "    pm.environment.set(\"refresh_token\", response.tokens.refresh);", "    pm.environment.set(\"user_id\", response.user.id);", "}"], "type": "text/javascript"}}]}, {"name": "Staff <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"your-password-here\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login/email/", "host": ["{{base_url}}"], "path": ["api", "auth", "login", "email", ""]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set(\"access_token\", response.tokens.access);", "    pm.environment.set(\"refresh_token\", response.tokens.refresh);", "}"], "type": "text/javascript"}}]}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"refresh\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/logout/", "host": ["{{base_url}}"], "path": ["api", "auth", "logout", ""]}}}]}, {"name": "User Profile", "item": [{"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/profile/", "host": ["{{base_url}}"], "path": ["api", "auth", "profile", ""]}}}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/profile/", "host": ["{{base_url}}"], "path": ["api", "auth", "profile", ""]}}}]}, {"name": "Address Management", "item": [{"name": "Get User Addresses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/addresses/", "host": ["{{base_url}}"], "path": ["api", "auth", "addresses", ""]}}}, {"name": "Create Address", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"address_type\": \"WORK\",\n    \"street\": \"456 Business District, Floor 5\",\n    \"city\": \"Delhi\",\n    \"state\": \"Delhi\",\n    \"zip_code\": \"110001\",\n    \"landmark\": \"Near Metro Station\",\n    \"is_default\": false\n}"}, "url": {"raw": "{{base_url}}/api/auth/addresses/", "host": ["{{base_url}}"], "path": ["api", "auth", "addresses", ""]}}}]}], "variable": [{"key": "base_url", "value": "http://localhost:8000"}]}