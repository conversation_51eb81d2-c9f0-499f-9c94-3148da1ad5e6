#!/usr/bin/env python
"""
Project health check script for Home Services Authentication Service
"""
import os
import sys
import django
from django.conf import settings
from django.core.management import execute_from_command_line
from django.db import connection
from django.core.cache import cache
import requests

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
django.setup()

from django.contrib.auth import get_user_model
from authentication.models import Address, FailedLoginAttempt

User = get_user_model()


def check_database():
    """Check database connection and basic queries"""
    print("🔍 Checking Database Connection...")
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        
        # Check if tables exist
        user_count = User.objects.count()
        address_count = Address.objects.count()
        failed_attempts_count = FailedLoginAttempt.objects.count()
        
        print(f"✅ Database connection successful")
        print(f"   - Users: {user_count}")
        print(f"   - Addresses: {address_count}")
        print(f"   - Failed Attempts: {failed_attempts_count}")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False


def check_redis():
    """Check Redis connection"""
    print("\n🔍 Checking Redis Connection...")
    try:
        cache.set('test_key', 'test_value', 10)
        value = cache.get('test_key')
        if value == 'test_value':
            print("✅ Redis connection successful")
            cache.delete('test_key')
            return True
        else:
            print("❌ Redis connection failed: Unable to retrieve test value")
            return False
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        return False


def check_environment_variables():
    """Check if all required environment variables are set"""
    print("\n🔍 Checking Environment Variables...")
    
    required_vars = [
        'SECRET_KEY',
        'DB_NAME',
        'DB_USER',
        'DB_PASSWORD',
        'MSG91_AUTH_KEY',
        'MSG91_TEMPLATE_ID',
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var) or os.getenv(var) in ['your-secret-key-here', 'your-db-password', 'your-msg91-auth-key', 'your-template-id']:
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing or default environment variables: {', '.join(missing_vars)}")
        return False
    else:
        print("✅ All required environment variables are set")
        return True


def check_django_settings():
    """Check Django settings configuration"""
    print("\n🔍 Checking Django Settings...")
    
    issues = []
    
    # Check DEBUG setting
    if settings.DEBUG:
        print("⚠️  DEBUG is enabled (OK for development)")
    
    # Check database engine
    if settings.DATABASES['default']['ENGINE'] != 'django.db.backends.postgresql':
        issues.append("Database engine is not PostgreSQL")
    
    # Check custom user model
    if settings.AUTH_USER_MODEL != 'authentication.User':
        issues.append("Custom user model not configured")
    
    # Check installed apps
    required_apps = [
        'rest_framework',
        'rest_framework_simplejwt',
        'corsheaders',
        'authentication',
    ]
    
    for app in required_apps:
        if app not in settings.INSTALLED_APPS:
            issues.append(f"Required app '{app}' not in INSTALLED_APPS")
    
    if issues:
        print(f"❌ Django settings issues: {', '.join(issues)}")
        return False
    else:
        print("✅ Django settings configured correctly")
        return True


def check_api_endpoints():
    """Check if API endpoints are accessible"""
    print("\n🔍 Checking API Endpoints...")
    
    try:
        # This would require the server to be running
        # For now, just check if URLs are configured
        from django.urls import reverse
        
        endpoints = [
            'authentication:register',
            'authentication:mobile_register',
            'authentication:email_login',
            'authentication:mobile_login',
            'authentication:send_otp',
            'authentication:verify_otp',
        ]
        
        for endpoint in endpoints:
            try:
                url = reverse(endpoint)
                print(f"✅ Endpoint '{endpoint}' configured: {url}")
            except Exception as e:
                print(f"❌ Endpoint '{endpoint}' not configured: {e}")
                return False
        
        return True
    except Exception as e:
        print(f"❌ Error checking endpoints: {e}")
        return False


def check_models():
    """Check if models are properly configured"""
    print("\n🔍 Checking Models...")
    
    try:
        # Check User model
        user_fields = [field.name for field in User._meta.fields]
        required_user_fields = ['email', 'mobile_number', 'name', 'user_type', 'is_verified']
        
        missing_fields = [field for field in required_user_fields if field not in user_fields]
        if missing_fields:
            print(f"❌ User model missing fields: {', '.join(missing_fields)}")
            return False
        
        # Check Address model
        address_fields = [field.name for field in Address._meta.fields]
        required_address_fields = ['user', 'address_type', 'street', 'city', 'is_default']
        
        missing_fields = [field for field in required_address_fields if field not in address_fields]
        if missing_fields:
            print(f"❌ Address model missing fields: {', '.join(missing_fields)}")
            return False
        
        print("✅ Models configured correctly")
        return True
    except Exception as e:
        print(f"❌ Error checking models: {e}")
        return False


def main():
    """Main health check function"""
    print("🏥 Home Services Authentication Service - Health Check")
    print("=" * 60)
    
    checks = [
        check_environment_variables,
        check_django_settings,
        check_database,
        check_redis,
        check_models,
        check_api_endpoints,
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        if check():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Health Check Results: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 All checks passed! Your project is ready to use.")
        print("\nNext steps:")
        print("1. Start Redis server if not running")
        print("2. Run: python manage.py runserver")
        print("3. Visit: http://localhost:8000/api/docs/")
    else:
        print("⚠️  Some checks failed. Please fix the issues above.")
        print("\nFor help, check the README.md file or run:")
        print("python setup_database.py")
    
    return passed == total


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
