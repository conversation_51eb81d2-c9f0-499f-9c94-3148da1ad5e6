#!/usr/bin/env python
"""
Clean migrations script for Home Services Authentication Service
This script will clean up migration conflicts
"""
import os
import sys
import django
import psycopg2

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
django.setup()

from django.conf import settings
from django.core.management import execute_from_command_line
from django.db import connection


def clean_migration_tables():
    """Clean migration tracking tables"""
    print("🧹 Cleaning migration tables...")
    
    try:
        with connection.cursor() as cursor:
            # Delete migration records for our app
            cursor.execute("DELETE FROM django_migrations WHERE app = 'authentication'")
            print("✅ Cleaned authentication migration records")
            
            # Check if our custom user table exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'auth_user'
                );
            """)
            table_exists = cursor.fetchone()[0]
            
            if table_exists:
                print("ℹ️  Custom user table already exists")
            else:
                print("ℹ️  Custom user table does not exist")
        
        return True
        
    except Exception as e:
        print(f"❌ Error cleaning migration tables: {e}")
        return False


def run_migrations():
    """Run migrations"""
    print("🔄 Running migrations...")
    
    try:
        # Apply migrations with fake-initial to handle existing tables
        execute_from_command_line(['manage.py', 'migrate', '--fake-initial'])
        
        print("✅ Migrations completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error running migrations: {e}")
        return False


def create_sample_user():
    """Create a sample superuser"""
    print("👤 Creating sample superuser...")
    
    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        # Check if any superuser exists
        if not User.objects.filter(is_superuser=True).exists():
            User.objects.create_superuser(
                email='<EMAIL>',
                name='Admin User',
                password='admin123'
            )
            print("✅ Superuser created successfully!")
            print("   Email: <EMAIL>")
            print("   Password: admin123")
        else:
            print("ℹ️  Superuser already exists")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating superuser: {e}")
        return False


def main():
    """Main function"""
    print("🧹 Home Services Authentication Service - Migration Cleanup")
    print("=" * 60)
    
    # Clean migration tables
    if not clean_migration_tables():
        print("❌ Migration cleanup failed.")
        sys.exit(1)
    
    # Run migrations
    if not run_migrations():
        print("❌ Migration failed.")
        sys.exit(1)
    
    # Create superuser
    if not create_sample_user():
        print("❌ Superuser creation failed.")
        sys.exit(1)
    
    print("\n✅ Migration cleanup completed successfully!")
    print("\nNext steps:")
    print("1. Start Redis server")
    print("2. Run: python manage.py runserver")
    print("3. Visit: http://localhost:8000/admin/")
    print("4. Visit: http://localhost:8000/api/docs/")


if __name__ == '__main__':
    main()
