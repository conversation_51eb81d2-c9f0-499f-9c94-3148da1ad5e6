#!/usr/bin/env python
"""
Create database script for Home Services Authentication Service
"""
import os
import sys
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
django.setup()

from django.conf import settings
from django.core.management import execute_from_command_line


def create_database():
    """Create the database if it doesn't exist"""
    db_settings = settings.DATABASES['default']
    
    print("🔄 Creating database...")
    print(f"Host: {db_settings['HOST']}")
    print(f"Port: {db_settings['PORT']}")
    print(f"User: {db_settings['USER']}")
    print(f"Database: {db_settings['NAME']}")
    
    try:
        # First, try to connect to the target database
        try:
            conn = psycopg2.connect(
                host=db_settings['HOST'],
                port=db_settings['PORT'],
                user=db_settings['USER'],
                password=db_settings['PASSWORD'],
                database=db_settings['NAME']
            )
            conn.close()
            print("✅ Database already exists!")
            return True
        except psycopg2.OperationalError as e:
            if "does not exist" in str(e):
                print("ℹ️  Database does not exist, creating it...")
            else:
                print(f"❌ Connection error: {e}")
                return False
        
        # Connect to PostgreSQL server (try different default databases)
        default_dbs = ['postgres', 'template1']
        conn = None
        
        for default_db in default_dbs:
            try:
                conn = psycopg2.connect(
                    host=db_settings['HOST'],
                    port=db_settings['PORT'],
                    user=db_settings['USER'],
                    password=db_settings['PASSWORD'],
                    database=default_db
                )
                print(f"✅ Connected to {default_db}")
                break
            except psycopg2.OperationalError:
                continue
        
        if not conn:
            print("❌ Could not connect to PostgreSQL server")
            return False
        
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Create the database
        print(f"🆕 Creating database '{db_settings['NAME']}'...")
        cursor.execute(f'CREATE DATABASE "{db_settings["NAME"]}"')
        
        cursor.close()
        conn.close()
        
        print("✅ Database created successfully!")
        return True
        
    except psycopg2.Error as e:
        if "already exists" in str(e):
            print("✅ Database already exists!")
            return True
        else:
            print(f"❌ Error creating database: {e}")
            return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def run_migrations():
    """Run migrations"""
    print("🔄 Running migrations...")
    
    try:
        # Make migrations
        execute_from_command_line(['manage.py', 'makemigrations'])
        
        # Apply migrations
        execute_from_command_line(['manage.py', 'migrate'])
        
        print("✅ Migrations completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error running migrations: {e}")
        return False


def create_superuser():
    """Create a superuser"""
    print("👤 Creating superuser...")
    
    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        if not User.objects.filter(email='<EMAIL>').exists():
            User.objects.create_superuser(
                email='<EMAIL>',
                name='Admin User',
                password='admin123'
            )
            print("✅ Superuser created successfully!")
            print("   Email: <EMAIL>")
            print("   Password: admin123")
        else:
            print("ℹ️  Superuser already exists")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating superuser: {e}")
        return False


def main():
    """Main function"""
    print("🚀 Home Services Authentication Service - Database Setup")
    print("=" * 60)
    
    # Create database
    if not create_database():
        print("❌ Database creation failed.")
        sys.exit(1)
    
    # Run migrations
    if not run_migrations():
        print("❌ Migration failed.")
        sys.exit(1)
    
    # Create superuser
    if not create_superuser():
        print("❌ Superuser creation failed.")
        sys.exit(1)
    
    print("\n✅ Database setup completed successfully!")
    print("\nNext steps:")
    print("1. Start Redis server (optional for development)")
    print("2. Run: python manage.py runserver")
    print("3. Visit: http://localhost:8000/admin/")
    print("4. Visit: http://localhost:8000/api/docs/")


if __name__ == '__main__':
    main()
