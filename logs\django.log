INFO 2025-06-11 13:03:24,786 autoreload 24508 17160 Watching for file changes with StatReloader
WARNING 2025-06-11 13:03:32,123 log 24508 19068 Not Found: /
WARNING 2025-06-11 13:03:32,124 basehttp 24508 19068 "GET / HTTP/1.1" 404 2682
INFO 2025-06-11 13:03:36,251 basehttp 24508 19068 "GET /admin HTTP/1.1" 301 0
INFO 2025-06-11 13:03:36,288 basehttp 24508 1384 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-06-11 13:03:36,363 basehttp 24508 1384 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4197
INFO 2025-06-11 13:03:36,476 basehttp 24508 23548 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
INFO 2025-06-11 13:03:36,477 basehttp 24508 18708 "GET /static/admin/css/login.css HTTP/1.1" 200 958
INFO 2025-06-11 13:03:36,485 basehttp 24508 23548 "GET /static/admin/css/responsive.css HTTP/1.1" 200 18533
INFO 2025-06-11 13:03:36,516 basehttp 24508 1384 "GET /static/admin/css/base.css HTTP/1.1" 200 21207
INFO 2025-06-11 13:03:36,525 basehttp 24508 10876 "GET /static/admin/js/theme.js HTTP/1.1" 200 1943
INFO 2025-06-11 13:03:36,549 basehttp 24508 17804 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
INFO 2025-06-11 13:03:36,557 basehttp 24508 23488 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
INFO 2025-06-11 13:03:44,731 backends 24508 1384 Successful email authentication for: <EMAIL>
ERROR 2025-06-11 13:03:48,943 log 24508 1384 Internal Server Error: /admin/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django_redis\cache.py", line 29, in _decorator
    return method(self, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django_redis\cache.py", line 138, in has_key
    return self.client.has_key(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django_redis\client\default.py", line 670, in has_key
    raise ConnectionInterrupted(connection=client) from e
django_redis.exceptions.ConnectionInterrupted: Redis ConnectionError: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\admin\sites.py", line 441, in login
    return LoginView.as_view(**defaults)(request)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\views\decorators\debug.py", line 92, in sensitive_post_parameters_wrapper
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\auth\views.py", line 90, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\views\generic\edit.py", line 153, in post
    return self.form_valid(form)
           ~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\auth\views.py", line 109, in form_valid
    auth_login(self.request, form.get_user())
    ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\auth\__init__.py", line 118, in login
    request.session.cycle_key()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\sessions\backends\base.py", line 304, in cycle_key
    self.create()
    ~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 42, in create
    self._session_key = self._get_new_session_key()
                        ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\sessions\backends\base.py", line 150, in _get_new_session_key
    if not self.exists(session_key):
           ~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 73, in exists
    bool(session_key) and (self.cache_key_prefix + session_key) in self._cache
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\cache\backends\base.py", line 299, in __contains__
    return self.has_key(key)
           ~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django_redis\cache.py", line 36, in _decorator
    raise e.__cause__
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django_redis\client\default.py", line 668, in has_key
    return client.exists(key) == 1
           ~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\redis\commands\core.py", line 1736, in exists
    return self.execute_command("EXISTS", *names)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\redis\client.py", line 533, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
                              ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\redis\connection.py", line 1086, in get_connection
    connection.connect()
    ~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\redis\connection.py", line 270, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
ERROR 2025-06-11 13:03:48,968 basehttp 24508 1384 "POST /admin/login/?next=/admin/ HTTP/1.1" 500 210899
INFO 2025-06-11 13:05:02,199 autoreload 24508 17160 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\home_services\settings.py changed, reloading.
INFO 2025-06-11 13:05:03,957 autoreload 4808 6884 Watching for file changes with StatReloader
INFO 2025-06-11 13:05:55,079 autoreload 4808 6884 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\home_services\settings.py changed, reloading.
INFO 2025-06-11 13:05:56,618 autoreload 14368 2856 Watching for file changes with StatReloader
INFO 2025-06-11 13:06:32,006 autoreload 9848 17552 Watching for file changes with StatReloader
INFO 2025-06-11 13:08:30,839 autoreload 5792 17732 Watching for file changes with StatReloader
INFO 2025-06-11 13:09:23,784 autoreload 5792 17732 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\home_services\settings.py changed, reloading.
INFO 2025-06-11 13:09:25,086 autoreload 22464 17160 Watching for file changes with StatReloader
INFO 2025-06-11 13:10:42,080 autoreload 14884 24628 Watching for file changes with StatReloader
INFO 2025-06-11 13:11:35,284 autoreload 14884 24628 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\home_services\settings.py changed, reloading.
INFO 2025-06-11 13:11:36,683 autoreload 23888 24596 Watching for file changes with StatReloader
INFO 2025-06-11 13:11:45,333 autoreload 23888 24596 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 13:11:46,797 autoreload 25592 8904 Watching for file changes with StatReloader
INFO 2025-06-11 13:11:55,609 autoreload 25592 8904 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 13:11:56,975 autoreload 25264 15680 Watching for file changes with StatReloader
INFO 2025-06-11 13:12:04,564 autoreload 25264 15680 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 13:12:06,521 autoreload 4544 25308 Watching for file changes with StatReloader
INFO 2025-06-11 13:12:15,244 autoreload 4544 25308 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 13:12:16,870 autoreload 23096 7820 Watching for file changes with StatReloader
INFO 2025-06-11 13:12:26,474 autoreload 23096 7820 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 13:12:27,931 autoreload 25464 21552 Watching for file changes with StatReloader
INFO 2025-06-11 13:12:38,614 autoreload 25464 21552 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 13:12:40,085 autoreload 14820 5684 Watching for file changes with StatReloader
INFO 2025-06-11 13:12:48,879 autoreload 14820 5684 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\home_services\settings.py changed, reloading.
INFO 2025-06-11 13:12:50,115 autoreload 10444 5600 Watching for file changes with StatReloader
INFO 2025-06-11 13:13:33,874 autoreload 19840 24192 Watching for file changes with StatReloader
INFO 2025-06-11 13:13:37,937 basehttp 19840 25516 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-06-11 13:13:38,015 basehttp 19840 25516 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4197
INFO 2025-06-11 13:13:38,063 basehttp 19840 25516 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
INFO 2025-06-11 13:13:38,071 basehttp 19840 25516 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
INFO 2025-06-11 13:13:38,075 basehttp 19840 19732 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
INFO 2025-06-11 13:13:38,081 basehttp 19840 24308 "GET /static/admin/css/base.css HTTP/1.1" 304 0
INFO 2025-06-11 13:13:38,092 basehttp 19840 9052 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
INFO 2025-06-11 13:13:38,096 basehttp 19840 18548 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
INFO 2025-06-11 13:13:38,096 basehttp 19840 24624 "GET /static/admin/css/login.css HTTP/1.1" 304 0
WARNING 2025-06-11 13:13:38,162 log 19840 19732 Not Found: /favicon.ico
INFO 2025-06-11 13:13:38,163 basehttp 19840 19732 - Broken pipe from ('127.0.0.1', 58888)
INFO 2025-06-11 13:13:45,111 backends 19840 25516 Successful email authentication for: <EMAIL>
INFO 2025-06-11 13:13:45,270 basehttp 19840 25516 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-06-11 13:13:45,504 basehttp 19840 25516 "GET /admin/ HTTP/1.1" 200 6789
INFO 2025-06-11 13:13:45,618 basehttp 19840 24308 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
INFO 2025-06-11 13:13:45,628 basehttp 19840 24308 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-06-11 13:13:45,628 basehttp 19840 18548 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
INFO 2025-06-11 13:13:45,629 basehttp 19840 9052 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
INFO 2025-06-11 13:13:51,968 basehttp 19840 25516 "GET /admin/authentication/user/ HTTP/1.1" 200 14407
INFO 2025-06-11 13:13:51,998 basehttp 19840 18548 "GET /static/admin/css/changelists.css HTTP/1.1" 200 6566
INFO 2025-06-11 13:13:52,005 basehttp 19840 24624 "GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
INFO 2025-06-11 13:13:52,006 basehttp 19840 25516 "GET /static/admin/js/core.js HTTP/1.1" 200 5682
INFO 2025-06-11 13:13:52,009 basehttp 19840 18548 "GET /static/admin/js/actions.js HTTP/1.1" 200 7872
INFO 2025-06-11 13:13:52,010 basehttp 19840 9052 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 292458
INFO 2025-06-11 13:13:52,012 basehttp 19840 25076 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 8943
INFO 2025-06-11 13:13:52,019 basehttp 19840 24624 "GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
INFO 2025-06-11 13:13:52,025 basehttp 19840 25516 "GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
INFO 2025-06-11 13:13:52,030 basehttp 19840 9052 "GET /static/admin/js/filters.js HTTP/1.1" 200 978
INFO 2025-06-11 13:13:52,034 basehttp 19840 18548 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 232381
INFO 2025-06-11 13:13:52,227 basehttp 19840 24308 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 13:13:52,231 basehttp 19840 9052 "GET /static/admin/img/search.svg HTTP/1.1" 200 458
INFO 2025-06-11 13:13:52,232 basehttp 19840 18548 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
INFO 2025-06-11 13:13:52,263 basehttp 19840 9052 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
INFO 2025-06-11 13:13:52,263 basehttp 19840 18548 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
INFO 2025-06-11 13:13:56,832 basehttp 19840 9052 "GET /admin/authentication/user/add/ HTTP/1.1" 200 13013
INFO 2025-06-11 13:13:56,862 basehttp 19840 9052 "GET /static/admin/css/forms.css HTTP/1.1" 200 9047
INFO 2025-06-11 13:13:56,865 basehttp 19840 9052 "GET /static/admin/js/change_form.js HTTP/1.1" 200 606
INFO 2025-06-11 13:13:56,867 basehttp 19840 25076 "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
INFO 2025-06-11 13:13:56,881 basehttp 19840 9052 "GET /static/admin/css/widgets.css HTTP/1.1" 200 11900
INFO 2025-06-11 13:13:57,063 basehttp 19840 18548 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 13:14:39,443 basehttp 19840 9052 "POST /admin/authentication/user/add/ HTTP/1.1" 302 0
INFO 2025-06-11 13:14:40,025 basehttp 19840 9052 "GET /admin/authentication/user/2/change/ HTTP/1.1" 200 23588
INFO 2025-06-11 13:14:40,059 basehttp 19840 25076 "GET /static/admin/js/collapse.js HTTP/1.1" 200 1803
INFO 2025-06-11 13:14:40,060 basehttp 19840 24624 "GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 200 19319
INFO 2025-06-11 13:14:40,060 basehttp 19840 24308 "GET /static/admin/js/SelectBox.js HTTP/1.1" 200 4530
INFO 2025-06-11 13:14:40,061 basehttp 19840 25516 "GET /static/admin/js/SelectFilter2.js HTTP/1.1" 200 15292
INFO 2025-06-11 13:14:40,071 basehttp 19840 18548 "GET /static/admin/js/calendar.js HTTP/1.1" 200 8466
INFO 2025-06-11 13:14:40,308 basehttp 19840 9052 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 13:14:40,386 basehttp 19840 18548 "GET /static/admin/img/icon-calendar.svg HTTP/1.1" 200 1086
INFO 2025-06-11 13:14:40,389 basehttp 19840 24624 "GET /static/admin/img/icon-clock.svg HTTP/1.1" 200 677
WARNING 2025-06-11 13:14:40,391 log 19840 9052 Not Found: /favicon.ico
INFO 2025-06-11 13:14:40,393 basehttp 19840 9052 - Broken pipe from ('127.0.0.1', 58890)
INFO 2025-06-11 13:14:49,491 basehttp 19840 18548 "GET /admin/authentication/user/ HTTP/1.1" 200 15073
INFO 2025-06-11 13:14:49,767 basehttp 19840 18548 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 13:14:49,779 basehttp 19840 24624 "GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
WARNING 2025-06-11 13:14:49,865 log 19840 18548 Not Found: /favicon.ico
INFO 2025-06-11 13:14:49,871 basehttp 19840 18548 - Broken pipe from ('127.0.0.1', 58891)
INFO 2025-06-11 13:14:56,756 basehttp 19840 24624 "GET /admin/authentication/user/2/change/ HTTP/1.1" 200 23334
INFO 2025-06-11 13:14:57,043 basehttp 19840 24624 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
WARNING 2025-06-11 13:14:57,111 log 19840 24624 Not Found: /favicon.ico
INFO 2025-06-11 13:14:57,112 basehttp 19840 24624 - Broken pipe from ('127.0.0.1', 58892)
INFO 2025-06-11 13:15:10,555 basehttp 19840 25516 "POST /admin/authentication/user/2/change/ HTTP/1.1" 302 0
INFO 2025-06-11 13:15:10,916 basehttp 19840 25516 "GET /admin/authentication/user/ HTTP/1.1" 200 15292
INFO 2025-06-11 13:15:11,188 basehttp 19840 25076 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
WARNING 2025-06-11 13:15:11,266 log 19840 25076 Not Found: /favicon.ico
INFO 2025-06-11 13:15:11,268 basehttp 19840 25076 - Broken pipe from ('127.0.0.1', 58901)
INFO 2025-06-11 13:15:21,055 basehttp 19840 25516 "GET /admin/auth/group/ HTTP/1.1" 200 8153
INFO 2025-06-11 13:15:21,297 basehttp 19840 24308 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
WARNING 2025-06-11 13:15:21,348 log 19840 24308 Not Found: /favicon.ico
INFO 2025-06-11 13:15:21,354 basehttp 19840 24308 - Broken pipe from ('127.0.0.1', 58889)
INFO 2025-06-11 13:15:24,268 basehttp 19840 25516 "GET /admin/auth/group/add/ HTTP/1.1" 200 12132
INFO 2025-06-11 13:15:24,519 basehttp 19840 25516 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 13:15:24,595 basehttp 19840 23288 "GET /static/admin/img/icon-unknown.svg HTTP/1.1" 200 655
INFO 2025-06-11 13:15:24,595 basehttp 19840 22820 "GET /static/admin/img/selector-icons.svg HTTP/1.1" 200 3291
INFO 2025-06-11 13:15:24,597 basehttp 19840 25516 "GET /static/admin/img/icon-unknown-alt.svg HTTP/1.1" 200 655
INFO 2025-06-11 13:15:28,492 basehttp 19840 25060 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-06-11 13:15:39,280 basehttp 19840 25516 "POST /admin/auth/group/add/ HTTP/1.1" 302 0
INFO 2025-06-11 13:15:39,607 basehttp 19840 25516 "GET /admin/auth/group/ HTTP/1.1" 200 9498
INFO 2025-06-11 13:15:39,875 basehttp 19840 22820 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 13:15:43,357 basehttp 19840 25516 "GET /admin/auth/group/add/ HTTP/1.1" 200 12132
INFO 2025-06-11 13:15:43,663 basehttp 19840 22820 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 13:16:25,086 basehttp 19840 25516 "GET /admin/authentication/user/ HTTP/1.1" 200 15065
INFO 2025-06-11 13:16:25,113 basehttp 19840 22820 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,115 basehttp 19840 23288 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,127 basehttp 19840 22820 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,127 basehttp 19840 23288 "GET /static/admin/js/core.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,129 basehttp 19840 6312 "GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,132 basehttp 19840 22820 "GET /static/admin/js/urlify.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,142 basehttp 19840 22392 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,142 basehttp 19840 23288 "GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,143 basehttp 19840 1672 "GET /static/admin/js/actions.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,144 basehttp 19840 6312 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,146 basehttp 19840 22820 "GET /static/admin/js/filters.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,148 basehttp 19840 22392 "GET /static/admin/css/base.css HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,151 basehttp 19840 23288 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,155 basehttp 19840 1672 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,159 basehttp 19840 6312 "GET /static/admin/css/changelists.css HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,162 basehttp 19840 22820 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,393 basehttp 19840 25516 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 13:16:25,401 basehttp 19840 22820 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
INFO 2025-06-11 13:16:25,431 basehttp 19840 22820 "GET /static/admin/img/search.svg HTTP/1.1" 200 458
INFO 2025-06-11 13:16:25,441 basehttp 19840 23288 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
INFO 2025-06-11 13:16:25,441 basehttp 19840 22820 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-06-11 13:16:25,442 basehttp 19840 22392 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
WARNING 2025-06-11 13:16:25,474 log 19840 22820 Not Found: /favicon.ico
INFO 2025-06-11 13:16:25,475 basehttp 19840 22820 - Broken pipe from ('127.0.0.1', 58954)
INFO 2025-06-11 13:16:30,278 basehttp 19840 25516 "GET /admin/authentication/user/2/change/ HTTP/1.1" 200 23384
INFO 2025-06-11 13:16:30,328 basehttp 19840 1672 "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:30,328 basehttp 19840 23288 "GET /static/admin/js/change_form.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:30,329 basehttp 19840 6312 "GET /static/admin/css/forms.css HTTP/1.1" 304 0
INFO 2025-06-11 13:16:30,345 basehttp 19840 23288 "GET /static/admin/css/widgets.css HTTP/1.1" 304 0
INFO 2025-06-11 13:16:30,540 basehttp 19840 22392 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
WARNING 2025-06-11 13:16:30,599 log 19840 22392 Not Found: /favicon.ico
INFO 2025-06-11 13:16:30,605 basehttp 19840 22392 - Broken pipe from ('127.0.0.1', 58981)
INFO 2025-06-11 13:16:41,535 basehttp 19840 25516 "POST /admin/authentication/user/2/change/ HTTP/1.1" 302 0
INFO 2025-06-11 13:16:41,829 basehttp 19840 25516 "GET /admin/authentication/user/ HTTP/1.1" 200 15292
INFO 2025-06-11 13:16:42,085 basehttp 19840 23288 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
WARNING 2025-06-11 13:16:42,132 log 19840 23288 Not Found: /favicon.ico
INFO 2025-06-11 13:16:42,136 basehttp 19840 23288 - Broken pipe from ('127.0.0.1', 58953)
WARNING 2025-06-11 13:17:01,393 log 19840 25516 Not Found: /api
WARNING 2025-06-11 13:17:01,394 basehttp 19840 25516 "GET /api HTTP/1.1" 404 2709
WARNING 2025-06-11 13:17:08,019 log 19840 25516 Not Found: /api/auth
WARNING 2025-06-11 13:17:08,019 basehttp 19840 25516 "GET /api/auth HTTP/1.1" 404 2724
INFO 2025-06-11 13:17:28,059 basehttp 19840 25516 "GET /api/docs HTTP/1.1" 301 0
INFO 2025-06-11 13:17:28,341 basehttp 19840 1672 "GET /api/docs/ HTTP/1.1" 200 4474
INFO 2025-06-11 13:17:29,126 basehttp 19840 1672 "GET /api/schema/ HTTP/1.1" 200 16225
INFO 2025-06-11 14:50:57,760 autoreload 26588 23052 Watching for file changes with StatReloader
