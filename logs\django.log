INFO 2025-06-11 13:03:24,786 autoreload 24508 17160 Watching for file changes with StatReloader
WARNING 2025-06-11 13:03:32,123 log 24508 19068 Not Found: /
WARNING 2025-06-11 13:03:32,124 basehttp 24508 19068 "GET / HTTP/1.1" 404 2682
INFO 2025-06-11 13:03:36,251 basehttp 24508 19068 "GET /admin HTTP/1.1" 301 0
INFO 2025-06-11 13:03:36,288 basehttp 24508 1384 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-06-11 13:03:36,363 basehttp 24508 1384 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4197
INFO 2025-06-11 13:03:36,476 basehttp 24508 23548 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
INFO 2025-06-11 13:03:36,477 basehttp 24508 18708 "GET /static/admin/css/login.css HTTP/1.1" 200 958
INFO 2025-06-11 13:03:36,485 basehttp 24508 23548 "GET /static/admin/css/responsive.css HTTP/1.1" 200 18533
INFO 2025-06-11 13:03:36,516 basehttp 24508 1384 "GET /static/admin/css/base.css HTTP/1.1" 200 21207
INFO 2025-06-11 13:03:36,525 basehttp 24508 10876 "GET /static/admin/js/theme.js HTTP/1.1" 200 1943
INFO 2025-06-11 13:03:36,549 basehttp 24508 17804 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
INFO 2025-06-11 13:03:36,557 basehttp 24508 23488 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
INFO 2025-06-11 13:03:44,731 backends 24508 1384 Successful email authentication for: <EMAIL>
ERROR 2025-06-11 13:03:48,943 log 24508 1384 Internal Server Error: /admin/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django_redis\cache.py", line 29, in _decorator
    return method(self, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django_redis\cache.py", line 138, in has_key
    return self.client.has_key(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django_redis\client\default.py", line 670, in has_key
    raise ConnectionInterrupted(connection=client) from e
django_redis.exceptions.ConnectionInterrupted: Redis ConnectionError: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\admin\sites.py", line 441, in login
    return LoginView.as_view(**defaults)(request)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\views\decorators\debug.py", line 92, in sensitive_post_parameters_wrapper
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\auth\views.py", line 90, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\views\generic\edit.py", line 153, in post
    return self.form_valid(form)
           ~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\auth\views.py", line 109, in form_valid
    auth_login(self.request, form.get_user())
    ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\auth\__init__.py", line 118, in login
    request.session.cycle_key()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\sessions\backends\base.py", line 304, in cycle_key
    self.create()
    ~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 42, in create
    self._session_key = self._get_new_session_key()
                        ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\sessions\backends\base.py", line 150, in _get_new_session_key
    if not self.exists(session_key):
           ~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 73, in exists
    bool(session_key) and (self.cache_key_prefix + session_key) in self._cache
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\cache\backends\base.py", line 299, in __contains__
    return self.has_key(key)
           ~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django_redis\cache.py", line 36, in _decorator
    raise e.__cause__
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django_redis\client\default.py", line 668, in has_key
    return client.exists(key) == 1
           ~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\redis\commands\core.py", line 1736, in exists
    return self.execute_command("EXISTS", *names)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\redis\client.py", line 533, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
                              ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\redis\connection.py", line 1086, in get_connection
    connection.connect()
    ~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\redis\connection.py", line 270, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
ERROR 2025-06-11 13:03:48,968 basehttp 24508 1384 "POST /admin/login/?next=/admin/ HTTP/1.1" 500 210899
INFO 2025-06-11 13:05:02,199 autoreload 24508 17160 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\home_services\settings.py changed, reloading.
INFO 2025-06-11 13:05:03,957 autoreload 4808 6884 Watching for file changes with StatReloader
INFO 2025-06-11 13:05:55,079 autoreload 4808 6884 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\home_services\settings.py changed, reloading.
INFO 2025-06-11 13:05:56,618 autoreload 14368 2856 Watching for file changes with StatReloader
INFO 2025-06-11 13:06:32,006 autoreload 9848 17552 Watching for file changes with StatReloader
INFO 2025-06-11 13:08:30,839 autoreload 5792 17732 Watching for file changes with StatReloader
INFO 2025-06-11 13:09:23,784 autoreload 5792 17732 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\home_services\settings.py changed, reloading.
INFO 2025-06-11 13:09:25,086 autoreload 22464 17160 Watching for file changes with StatReloader
