"""Auto-generated file, do not edit by hand. 995 metadata"""
from ..phonemetadata import NumberFormat

PHONE_ALT_FORMAT_995 = [NumberFormat(pattern='(\\d{3})(\\d{2})(\\d{2})(\\d{2})', format='\\1 \\2 \\3 \\4', leading_digits_pattern=['32']), NumberFormat(pattern='(\\d{2})(\\d)(\\d{2})(\\d{2})(\\d{2})', format='\\1 \\2 \\3 \\4 \\5', leading_digits_pattern=['32']), NumberFormat(pattern='(\\d{3})(\\d{6})', format='\\1 \\2', leading_digits_pattern=['[348]']), NumberFormat(pattern='(\\d{3})(\\d{3})(\\d{3})', format='\\1 \\2 \\3', leading_digits_pattern=['[348]']), NumberFormat(pattern='(\\d{2})(\\d{2})(\\d{3})(\\d{2})', format='\\1 \\2 \\3 \\4', leading_digits_pattern=['32']), NumberFormat(pattern='(\\d{5})(\\d{2})(\\d{2})', format='\\1 \\2 \\3', leading_digits_pattern=['32'])]
